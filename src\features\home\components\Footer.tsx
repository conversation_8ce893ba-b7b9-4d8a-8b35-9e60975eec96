import instagramIcon from "@/assets/icons/instagram.svg";
import countryLifeLogo from "@/assets/images/country-life-logo.svg";
import footerBackgroun from "@/assets/images/country-life-logo.svg";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import Link from "next/link";
import { AiOutlineTikTok } from "react-icons/ai";

const Footer = () => {
  return (
    <section>
      <div className="max-w-[90rem] p-[2rem] lg:px-[6rem] lg:py-[4rem]">
        <div className="flex flex-col md:items-center md:justify-center">
          <Image src={countryLifeLogo} alt="Country Life Logo" className="w-[8rem] lg:w-[11.25rem]" />
          <div className="mt-4 flex flex-col justify-between gap-3 md:flex-row lg:gap-8">
            <Link href="#" className="font-medium text-[#2D3131]">
              Home
            </Link>
            <Link href="#" className="font-medium text-[#2D3131]">
              Our Story
            </Link>
            <Link href="#" className="font-medium text-[#2D3131]">
              Shop Now
            </Link>
            <Link href="#" className="font-medium text-[#2D3131]">
              Wellness Blog
            </Link>
            <Link href="#" className="font-medium text-[#2D3131]">
              Contact Us
            </Link>
          </div>
          <div className="mt-8 flex items-center gap-4">
            <Link
              className="flex size-[1.875rem] items-center justify-center rounded-full bg-[#A5BD3A]"
              href="https://www.instagram.com/countrylifehq/"
              target="blank"
            >
              <Image src={instagramIcon} alt="Instagram Icon" />
            </Link>
            <Link
              className="flex size-[1.875rem] items-center justify-center rounded-full bg-[#A5BD3A]"
              href="https://www.tiktok.com/@countrylifehq?_t=zm-8x41zaqzeo2&_r=1"
              target="blank"
            >
              <AiOutlineTikTok color="#fff" size={12} />
            </Link>
          </div>
          <Separator className="my-6 bg-[#1A1A1A]" />
        </div>
        <div className="flex flex-col-reverse gap-5 md:flex-row md:items-center md:justify-between">
          <div>
            <p className="text-sm text-[#1A1A1A]">© 2025 Country Life. All rights reserved.</p>
          </div>
          <div className="flex flex-col gap-3 md:flex-row md:items-center">
            <Link href="#" className="text-sm text-[#1A1A1A] underline">
              Privacy Policy
            </Link>
            <Link href="#" className="text-sm text-[#1A1A1A] underline">
              Terms of Service
            </Link>
            <Link href="#" className="text-sm text-[#1A1A1A] underline">
              Cookies Settings
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Footer;
