import Image from "next/image";
import Link from "next/link";
import organicProductsImage from "@/assets/images/organic-products.jpg";

const ExploreProducts = () => {
  return (
    <section>
      <div className="max-w-[90rem] px-[2rem] lg:px-[4rem] pt-[2rem]">
        <p className="text-center text-lg font-bold text-[#1A1A1A] md:text-2xl lg:text-[2.8rem]">
          Explore products suited to your lifestyle and health.
        </p>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-8">
          <div className="row-span-2 rounded-[16px] bg-[#FFF6EA] px-4 py-4">
            <div className="flex items-center gap-2">
              <div>
                <p className="md:text-lg lg:text-4xl">Fresh, Organic Products for Everyday Wellness</p>
                <p className="mt-2">Make mindful choices that nourish your body </p>
                <Link
                  className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#4D8526] font-medium text-white"
                  href="https://countrylife676.bumpa.shop/"
                >
                  Shop Now
                </Link>
                {/* <Image src={organicProductsImage} alt="Organic Products" className="w-full h-full" /> */}
              </div>
            </div>
          </div>
          <div className="rounded-[16px] bg-[#E2E7CD] px-4 py-4">
            <div>
              <p className="md:text-lg lg:text-4xl">Curated Picks for Every Diet</p>
              <p className="mt-2">Discover products tailored to your wellness journey. </p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#BD6703] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
              >
                Shop Now
              </Link>
            </div>
          </div>
          <div className="rounded-[16px] bg-[#FFE9E9] px-4 py-4">
            <div>
              <p className="md:text-lg lg:text-4xl">Personalized Bundles for You and Your Family</p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#4D8526] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
              >
                Shop Now
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreProducts;
